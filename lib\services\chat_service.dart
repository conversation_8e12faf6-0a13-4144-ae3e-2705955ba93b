import 'dart:async';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/chat.dart';
import '../models/message.dart';
import 'firebase_service.dart';
import 'notification_service.dart';

class ChatService {
  final FirebaseService _firebaseService;
  final NotificationService _notificationService = NotificationService();
  final Map<String, StreamSubscription> _messageSubscriptions = {};
  final Map<String, StreamSubscription> _chatSubscriptions = {};

  ChatService(this._firebaseService);

  // Get current user ID
  String? get currentUserId => _firebaseService.currentUser?.uid;

  // Create a new direct chat between two users
  Future<String> createDirectChat(
    String otherUserId,
    String otherUserName,
    String? otherUserAvatar,
  ) async {
    if (currentUserId == null) throw Exception('User not authenticated');

    // Check if chat already exists
    final existingChat = await _findExistingDirectChat(otherUserId);
    if (existingChat != null) {
      return existingChat.id;
    }

    // Get current user data
    final currentUser = _firebaseService.currentUser!;
    final currentUserDoc = await _firebaseService.usersCollection
        .doc(currentUserId)
        .get();
    final currentUserData = currentUserDoc.data() as Map<String, dynamic>?;

    // Create new chat
    final chatRef = _firebaseService.chatsCollection.doc();
    final now = DateTime.now();

    final chat = Chat(
      id: chatRef.id,
      type: ChatType.direct,
      participants: [
        ChatParticipant(
          userId: currentUserId!,
          name:
              currentUserData?['fullName'] ??
              currentUser.displayName ??
              'Unknown',
          avatar: currentUserData?['photoURL'],
          joinedAt: now,
          isOnline: true,
        ),
        ChatParticipant(
          userId: otherUserId,
          name: otherUserName,
          avatar: otherUserAvatar,
          joinedAt: now,
        ),
      ],
      createdAt: now,
      updatedAt: now,
      unreadCounts: {currentUserId!: 0, otherUserId: 0},
      createdBy: currentUserId,
    );

    await chatRef.set(chat.toFirestore());
    return chatRef.id;
  }

  // Find existing direct chat between current user and another user
  Future<Chat?> _findExistingDirectChat(String otherUserId) async {
    if (currentUserId == null) return null;

    final query = await _firebaseService.chatsCollection
        .where('type', isEqualTo: 'direct')
        .where(
          'participants',
          arrayContainsAny: [
            {'userId': currentUserId},
            {'userId': otherUserId},
          ],
        )
        .get();

    for (final doc in query.docs) {
      final chat = Chat.fromFirestore(doc);
      final participantIds = chat.participants.map((p) => p.userId).toList();
      if (participantIds.contains(currentUserId) &&
          participantIds.contains(otherUserId)) {
        return chat;
      }
    }

    return null;
  }

  // Send a text message
  Future<void> sendMessage(
    String chatId,
    String content, {
    MessageType type = MessageType.text,
  }) async {
    if (currentUserId == null) throw Exception('User not authenticated');

    final currentUser = _firebaseService.currentUser!;
    final currentUserDoc = await _firebaseService.usersCollection
        .doc(currentUserId)
        .get();
    final currentUserData = currentUserDoc.data() as Map<String, dynamic>?;

    final messageRef = _firebaseService.firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc();

    final message = Message(
      id: messageRef.id,
      chatId: chatId,
      senderId: currentUserId!,
      senderName:
          currentUserData?['fullName'] ?? currentUser.displayName ?? 'Unknown',
      senderAvatar: currentUserData?['photoURL'],
      content: content,
      type: type,
      status: MessageStatus.sending,
      timestamp: DateTime.now(),
    );

    // Add message to subcollection
    await messageRef.set(message.toFirestore());

    // Update chat's last message and timestamp
    await _updateChatLastMessage(chatId, message);

    // Update message status to sent
    await messageRef.update({'status': MessageStatus.sent.name});

    // Send notification to other participants
    await _sendNotificationToParticipants(chatId, message);
  }

  // Send image message
  Future<void> sendImageMessage(
    String chatId,
    File imageFile,
    String? caption,
  ) async {
    if (currentUserId == null) throw Exception('User not authenticated');

    // Upload image to Firebase Storage
    final fileName =
        'chat_images/$chatId/${DateTime.now().millisecondsSinceEpoch}.jpg';
    final storageRef = _firebaseService.storage.ref().child(fileName);

    final uploadTask = storageRef.putFile(imageFile);
    final snapshot = await uploadTask;
    final imageUrl = await snapshot.ref.getDownloadURL();

    // Send message with image URL
    final currentUser = _firebaseService.currentUser!;
    final currentUserDoc = await _firebaseService.usersCollection
        .doc(currentUserId)
        .get();
    final currentUserData = currentUserDoc.data() as Map<String, dynamic>?;

    final messageRef = _firebaseService.firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc();

    final message = Message(
      id: messageRef.id,
      chatId: chatId,
      senderId: currentUserId!,
      senderName:
          currentUserData?['fullName'] ?? currentUser.displayName ?? 'Unknown',
      senderAvatar: currentUserData?['photoURL'],
      content: caption ?? '',
      type: MessageType.image,
      status: MessageStatus.sent,
      timestamp: DateTime.now(),
      imageUrl: imageUrl,
    );

    await messageRef.set(message.toFirestore());
    await _updateChatLastMessage(chatId, message);
  }

  // Update chat's last message
  Future<void> _updateChatLastMessage(String chatId, Message message) async {
    await _firebaseService.chatsCollection.doc(chatId).update({
      'lastMessage': message.toFirestore(),
      'updatedAt': Timestamp.fromDate(DateTime.now()),
    });
  }

  // Get messages stream for a chat
  Stream<List<Message>> getMessagesStream(String chatId) {
    return _firebaseService.firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Message.fromFirestore(doc)).toList(),
        );
  }

  // Get chats stream for current user
  Stream<List<Chat>> getChatsStream() {
    if (currentUserId == null) return Stream.value([]);

    return _firebaseService.chatsCollection
        .where(
          'participants',
          arrayContainsAny: [
            {'userId': currentUserId},
          ],
        )
        .orderBy('updatedAt', descending: true)
        .snapshots()
        .map(
          (snapshot) =>
              snapshot.docs.map((doc) => Chat.fromFirestore(doc)).toList(),
        );
  }

  // Mark messages as read
  Future<void> markMessagesAsRead(String chatId) async {
    if (currentUserId == null) return;

    final batch = _firebaseService.firestore.batch();

    // Get unread messages
    final unreadMessages = await _firebaseService.firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .where('senderId', isNotEqualTo: currentUserId)
        .where('status', isNotEqualTo: MessageStatus.read.name)
        .get();

    // Mark messages as read
    for (final doc in unreadMessages.docs) {
      batch.update(doc.reference, {
        'status': MessageStatus.read.name,
        'readAt': Timestamp.fromDate(DateTime.now()),
      });
    }

    // Reset unread count for current user
    batch.update(_firebaseService.chatsCollection.doc(chatId), {
      'unreadCounts.$currentUserId': 0,
    });

    await batch.commit();
  }

  // Set typing status
  Future<void> setTypingStatus(String chatId, bool isTyping) async {
    if (currentUserId == null) return;

    await _firebaseService.chatsCollection.doc(chatId).update({
      'participants': FieldValue.arrayRemove([
        {'userId': currentUserId, 'isTyping': !isTyping},
      ]),
    });

    await _firebaseService.chatsCollection.doc(chatId).update({
      'participants': FieldValue.arrayUnion([
        {'userId': currentUserId, 'isTyping': isTyping},
      ]),
    });
  }

  // Search users for starting new chats
  Future<List<Map<String, dynamic>>> searchUsers(String query) async {
    if (query.isEmpty) return [];

    final results = await _firebaseService.usersCollection
        .where('fullName', isGreaterThanOrEqualTo: query)
        .where('fullName', isLessThan: '${query}z')
        .limit(20)
        .get();

    return results.docs
        .where((doc) => doc.id != currentUserId) // Exclude current user
        .map((doc) => {'id': doc.id, ...doc.data() as Map<String, dynamic>})
        .toList();
  }

  // Delete a chat
  Future<void> deleteChat(String chatId) async {
    if (currentUserId == null) return;

    // Delete all messages in the chat
    final messages = await _firebaseService.firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .get();

    final batch = _firebaseService.firestore.batch();
    for (final doc in messages.docs) {
      batch.delete(doc.reference);
    }

    // Delete the chat document
    batch.delete(_firebaseService.chatsCollection.doc(chatId));

    await batch.commit();
  }

  // Send notification to other participants in the chat
  Future<void> _sendNotificationToParticipants(
    String chatId,
    Message message,
  ) async {
    try {
      // Get chat document to find participants
      final chatDoc = await _firebaseService.chatsCollection.doc(chatId).get();
      if (!chatDoc.exists) return;

      final chat = Chat.fromFirestore(chatDoc);

      // Send notification to all participants except the sender
      for (final participant in chat.participants) {
        if (participant.userId != message.senderId) {
          await _notificationService.sendNotificationToUser(
            userId: participant.userId,
            title: message.senderName,
            body: message.type == MessageType.image
                ? '📷 Sent a photo'
                : message.content,
            chatId: chatId,
            senderId: message.senderId,
          );
        }
      }
    } catch (e) {
      print('Error sending notifications: $e');
    }
  }

  // Dispose subscriptions
  void dispose() {
    for (final subscription in _messageSubscriptions.values) {
      subscription.cancel();
    }
    for (final subscription in _chatSubscriptions.values) {
      subscription.cancel();
    }
    _messageSubscriptions.clear();
    _chatSubscriptions.clear();
  }
}
